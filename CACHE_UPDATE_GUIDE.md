# 缓存更新方案使用指南

## 概述

本方案实现了导航数据的智能缓存更新机制，解决了浏览器缓存导致数据更新不及时的问题。

## 核心策略

### 1. 配置文件强制刷新
- **目标文件**: `nav/data/appconfig.json`
- **策略**: 每次请求都附加当前时间戳参数
- **效果**: 确保总是获取最新的配置文件

### 2. 数据文件版本化缓存
- **目标文件**: 所有数据源文件（如 `foneshare-faci.json` 等）
- **策略**: 使用配置文件中的 `dataVersion` 字段作为缓存版本号
- **效果**: 只有当版本号变更时，浏览器才会下载新数据

## 实施详情

### 步骤 1: 配置文件版本控制

在 `nav/data/appconfig.json` 中添加了 `dataVersion` 字段：

```json
{
  "version": "3.0",
  "description": "FaciShare 导航数据配置文件",
  "lastUpdated": "2025-08-11",
  "dataVersion": "2025081221",
  "dataSources": [
    // ... 数据源配置
  ]
}
```

### 步骤 2: 主应用缓存逻辑

修改了 `nav/js/app.js` 中的数据加载逻辑：

#### 配置文件加载（强制刷新）
```javascript
// 使用时间戳强制刷新配置文件
const configUrl = `./nav/data/appconfig.json?v=${Date.now()}`;
const configResponse = await fetch(configUrl);
```

#### 数据文件加载（版本化缓存）
```javascript
// 使用版本号进行缓存控制
const separator = path.includes('?') ? '&' : '?';
const finalUrl = `${path}${separator}v=${this.dataVersion}`;
const response = await fetch(finalUrl);
```

### 步骤 3: 时间通知缓存逻辑

修改了 `nav/js/time-notification.js` 中的配置加载：

```javascript
// 配置文件强制刷新
const appConfigUrl = `nav/data/appconfig.json?v=${Date.now()}`;

// 时间通知配置文件版本化缓存
const versionedConfigUrl = `${configPath}${separator}v=${dataVersion}`;
```

## 使用方法

### 更新数据时的操作流程

1. **修改数据文件**（如 `foneshare-faci.json`）
2. **更新版本号**：修改 `appconfig.json` 中的 `dataVersion` 字段
3. **用户访问**：用户刷新页面时会自动获取最新数据

### 版本号格式建议

推荐使用 `年月日时` 格式，例如：
- `2025081221` = 2025年8月12日21时
- 便于识别更新时间
- 确保版本号唯一性

## 测试验证

### 网络请求验证

通过浏览器开发者工具的网络面板，可以观察到：

1. **配置文件请求**：
   ```
   GET /nav/data/appconfig.json?v=1755005030456
   ```
   - 每次都有不同的时间戳参数
   - 返回状态码 200（非缓存）

2. **数据文件请求**：
   ```
   GET /nav/data/foneshare-faci.json?v=2025081221
   ```
   - 使用固定的版本号参数
   - 版本号不变时返回 304（使用缓存）
   - 版本号变更时返回 200（下载新数据）

### 测试页面

项目中包含了测试页面 `simple-test.html`，可以用来验证缓存策略：

```bash
# 启动本地服务器
python3 -m http.server 8000

# 访问测试页面
http://localhost:8000/simple-test.html
```

## 优势

1. **智能缓存**：数据未变时使用缓存，提高加载速度
2. **及时更新**：数据变更时强制更新，确保内容最新
3. **向下兼容**：不影响现有功能，平滑升级
4. **易于维护**：只需更新版本号即可控制缓存

## 注意事项

1. **版本号管理**：每次数据更新都要记得更新 `dataVersion`
2. **时间同步**：建议使用统一的时间格式
3. **降级处理**：配置加载失败时会使用时间戳作为版本号
4. **网络监控**：可通过浏览器开发者工具监控缓存效果

## 故障排除

### 数据未更新
1. 检查 `dataVersion` 是否已更新
2. 确认网络请求中包含正确的版本参数
3. 清除浏览器缓存后重试

### 加载失败
1. 检查配置文件格式是否正确
2. 确认数据文件路径是否存在
3. 查看浏览器控制台错误信息

{"categories": [{"id": "foneshare-cloud", "name": "多云管理", "icon": "🌐", "children": [{"id": "foneshare-cloud-private", "name": "应用专属", "icon": "⛅️", "children": [{"id": "foneshare-cloud-private-sbt", "name": "双胞胎", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-crm-sbtjt-home", "name": "官网", "description": "纷享双胞胎官网登录", "iconClass": "fas fa-house-chimney", "url": "https://crm.sbtjt.com/", "tags": ["官网", "登录", "双胞胎"]}, {"id": "foneshare-cloud-private-sbt-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://sbt-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "双胞胎"]}, {"id": "foneshare-cloud-private-sbt-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://sbt-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "双胞胎"]}, {"id": "foneshare-cloud-private-sbt-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-sitemap", "url": "https://sbt-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "双胞胎"]}, {"id": "foneshare-cloud-private-sbt-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://sbt-prod.foneshare.cn/grafana", "tags": ["grafana", "dashboard", "双胞胎"]}, {"id": "foneshare-cloud-private-sbt-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-tasks", "url": "https://sbt-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "双胞胎"]}, {"id": "foneshare-cloud-private-sbt-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://sbt-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "双胞胎"]}, {"id": "foneshare-cloud-private-sbt-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-magnifying-glass-chart", "url": "https://sbt-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "双胞胎"]}, {"id": "foneshare-cloud-private-sbt-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://sbt-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "双胞胎"]}]}, {"id": "foneshare-cloud-private-unicloudea", "name": "紫光云", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-crm-unicloudea-home", "name": "官网", "description": "纷享紫光云官网登录", "iconClass": "fas fa-house-chimney", "url": "https://crm.unicloudea.com/", "tags": ["官网", "登录", "紫光云"]}, {"id": "foneshare-cloud-private-unicloudea-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://ucd-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "紫光云"]}, {"id": "foneshare-cloud-private-unicloudea-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://ucd-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "紫光云"]}, {"id": "foneshare-cloud-private-unicloudea-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-sitemap", "url": "https://ucd-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "紫光云"]}, {"id": "foneshare-cloud-private-unicloudea-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "紫光云"]}, {"id": "foneshare-cloud-private-unicloudea-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-tasks", "url": "https://ucd-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "紫光云"]}, {"id": "foneshare-cloud-private-unicloudea-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://ucd-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "紫光云"]}, {"id": "foneshare-cloud-private-unicloudea-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-magnifying-glass-chart", "url": "https://ucd-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "紫光云"]}, {"id": "foneshare-cloud-private-unicloudea-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://ucd-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "紫光云"]}]}, {"id": "foneshare-cloud-private-xjgc", "name": "许继云", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-crm-xjgc-home", "name": "官网", "description": "纷享许继云官网登录", "iconClass": "fas fa-house-chimney", "url": "https://crm.xjgc.com/", "tags": ["官网", "登录", "许继云"]}, {"id": "foneshare-cloud-private-xjgc-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://xjgc-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "许继云"]}, {"id": "foneshare-cloud-private-xjgc-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://xjgc-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "许继云"]}, {"id": "foneshare-cloud-private-xjgc-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-sitemap", "url": "https://xjgc-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "许继云"]}, {"id": "foneshare-cloud-private-xjgc-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-tasks", "url": "https://xjgc-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "许继云"]}, {"id": "foneshare-cloud-private-xjgc-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://xjgc-prod.foneshare.cn/grafana", "tags": ["grafana", "dashboard", "许继云"]}, {"id": "foneshare-cloud-private-xjgc-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://xjgc-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "许继云"]}, {"id": "foneshare-cloud-private-xjgc-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-magnifying-glass-chart", "url": "https://xjgc-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "许继云"]}, {"id": "foneshare-cloud-private-xjgc-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://xjgc-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "许继云"]}]}, {"id": "foneshare-cloud-private-hisense", "name": "海信云", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-xiaoke-hisense-home", "name": "官网", "description": "纷享海信云官网登录", "iconClass": "fas fa-house-chimney", "url": "https://xiaoke.hisense.com/", "tags": ["官网", "登录", "海信云"]}, {"id": "foneshare-cloud-private-hisense-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://hisense-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "海信云"]}, {"id": "foneshare-cloud-private-hisense-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://hisense-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "海信云"]}, {"id": "foneshare-cloud-private-hisense-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-sitemap", "url": "https://hisense-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "海信云"]}, {"id": "foneshare-cloud-private-hisense-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://hisense-prod.foneshare.cn/grafana", "tags": ["grafana", "dashboard", "海信云"]}, {"id": "foneshare-cloud-private-hisense-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-tasks", "url": "https://hisense-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "海信云"]}, {"id": "foneshare-cloud-private-hisense-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://hisense-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "海信云"]}, {"id": "foneshare-cloud-private-hisense-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-magnifying-glass-chart", "url": "https://hisense-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "海信云"]}, {"id": "foneshare-cloud-private-hisense-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://hisense-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "海信云"]}]}, {"id": "foneshare-cloud-private-chinatower", "name": "铁塔云", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-zlbss-crm-chinatowercom-home", "name": "官网", "description": "纷享铁塔云官网登录", "iconClass": "fas fa-house-chimney", "url": "https://zlbss-crm.chinatowercom.cn/", "tags": ["官网", "登录", "铁塔云"]}, {"id": "foneshare-cloud-private-chinatower-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://chinatower-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "铁塔云"]}, {"id": "foneshare-cloud-private-chinatower-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://chinatower-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "铁塔云"]}, {"id": "foneshare-cloud-private-chinatower-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-sitemap", "url": "https://chinatower-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "铁塔云"]}, {"id": "foneshare-cloud-private-chinatower-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://chinatower-prod.foneshare.cn/grafana", "tags": ["grafana", "dashboard", "铁塔云"]}, {"id": "foneshare-cloud-private-chinatower-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-tasks", "url": "https://chinatower-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "铁塔云"]}, {"id": "foneshare-cloud-private-chinatower-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://chinatower-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "铁塔云"]}, {"id": "foneshare-cloud-private-chinatower-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-magnifying-glass-chart", "url": "https://chinatower-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "铁塔云"]}, {"id": "foneshare-cloud-private-chinatower-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://chinatower-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "铁塔云"]}]}, {"id": "foneshare-cloud-privates-msv", "name": "蒙牛云", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-msv-mengniu-home", "name": "官网", "description": "纷享蒙牛云官网登录", "iconClass": "fas fa-house-chimney", "url": "https://msv.mengniu.cn/", "tags": ["官网", "登录", "蒙牛云"]}, {"id": "foneshare-cloud-privates-msv-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://mengniu-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "蒙牛云"]}, {"id": "foneshare-cloud-privates-msv-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://mengniu-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "蒙牛云"]}, {"id": "foneshare-cloud-privates-msv-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-sitemap", "url": "https://mengniu-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "蒙牛云"]}, {"id": "foneshare-cloud-privates-msv-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，数据源需选择mengniu-prometheus。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "蒙牛云"]}, {"id": "foneshare-cloud-privates-msv-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-tasks", "url": "https://mengniu-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "蒙牛云"]}, {"id": "foneshare-cloud-privates-msv-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://mengniu-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "蒙牛云"]}, {"id": "foneshare-cloud-privates-msv-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-magnifying-glass-chart", "url": "https://mengniu-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "蒙牛云"]}, {"id": "foneshare-cloud-privates-msv-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://mengniu-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "蒙牛云"]}]}, {"id": "foneshare-cloud-private-hevision", "name": "何氏眼科", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-crm-hevision-home", "name": "官网", "description": "纷享何氏眼科官网登录", "iconClass": "fas fa-house-chimney", "url": "https://crm.hevision.com/", "tags": ["官网", "登录", "何氏眼科"]}, {"id": "foneshare-cloud-private-hevision-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://hsyk-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "何氏眼科"]}, {"id": "foneshare-cloud-private-hevision-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://hsyk-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "何氏眼科"]}, {"id": "foneshare-cloud-private-hevision-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-sitemap", "url": "https://hsyk-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "何氏眼科"]}, {"id": "foneshare-cloud-private-hevision-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，数据源需选择heyk-prometheus。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "何氏眼科"]}, {"id": "foneshare-cloud-private-hevision-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-tasks", "url": "https://hsyk-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "何氏眼科"]}, {"id": "foneshare-cloud-private-hevision-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://hsyk-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "何氏眼科"]}, {"id": "foneshare-cloud-private-hevision-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-magnifying-glass-chart", "url": "https://hsyk-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "何氏眼科"]}, {"id": "foneshare-cloud-private-hevision-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://hsyk-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "何氏眼科"]}]}, {"id": "foneshare-cloud-private-wzz", "name": "伍子醉", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-crm-hnwzz-home", "name": "官网", "description": "纷享伍子醉官网登录", "iconClass": "fas fa-house-chimney", "url": "https://crm.hnwzz.cn/", "tags": ["官网", "登录", "伍子醉"]}, {"id": "foneshare-cloud-private-wzz-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://wuzizui99-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "伍子醉"]}, {"id": "foneshare-cloud-private-wzz-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://wuzizui99-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "伍子醉"]}, {"id": "foneshare-cloud-private-wzz-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-sitemap", "url": "https://wuzizui99-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "伍子醉"]}, {"id": "foneshare-cloud-private-wzz-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "伍子醉"]}, {"id": "foneshare-cloud-private-wzz-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-tasks", "url": "https://wuzizui99-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "伍子醉"]}, {"id": "foneshare-cloud-private-wzz-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://wuzizui99-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "伍子醉"]}, {"id": "foneshare-cloud-private-wzz-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-magnifying-glass-chart", "url": "https://wuzizui99-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "伍子醉"]}, {"id": "foneshare-cloud-private-wzz-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://wuzizui99-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "伍子醉"]}]}, {"id": "foneshare-cloud-private-iflytek", "name": "科大讯飞", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-crm-iflytek-home", "name": "官网", "description": "纷享科大讯飞官网登录", "iconClass": "fas fa-house-chimney", "url": "https://crm.iflytek.com/", "tags": ["官网", "登录", "科大讯飞"]}, {"id": "foneshare-cloud-private-iflytek-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://iflytek-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "科大讯飞"]}, {"id": "foneshare-cloud-private-iflytek-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://iflytek-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "科大讯飞"]}, {"id": "foneshare-cloud-private-iflytek-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-server", "url": "https://iflytek-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "科大讯飞"]}, {"id": "foneshare-cloud-private-iflytek-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "科大讯飞"]}, {"id": "foneshare-cloud-private-iflytek-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://iflytek-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "科大讯飞"]}, {"id": "foneshare-cloud-private-iflytek-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-cubes", "url": "https://iflytek-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "科大讯飞"]}, {"id": "foneshare-cloud-private-iflytek-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://iflytek-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "科大讯飞"]}, {"id": "foneshare-cloud-private-iflytek-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://iflytek-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "科大讯飞"]}]}, {"id": "foneshare-cloud-private-hexagonmi", "name": "海克思康", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-crm-hexagonmi-home", "name": "官网", "description": "纷享海克思康官网登录", "iconClass": "fas fa-house-chimney", "url": "https://crm.hexagonmi.com.cn/", "tags": ["官网", "登录", "海克思康"]}, {"id": "foneshare-cloud-private-hexagonmi-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://hexagonmi-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "海克思康"]}, {"id": "foneshare-cloud-private-hexagonmi-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://hexagonmi-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "海克思康"]}, {"id": "foneshare-cloud-private-hexagonmi-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-server", "url": "https://hexagonmi-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "海克思康"]}, {"id": "foneshare-cloud-private-hexagonmi-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "海克思康"]}, {"id": "foneshare-cloud-private-hexagonmi-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://hexagonmi-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "海克思康"]}, {"id": "foneshare-cloud-private-hexagonmi-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-cubes", "url": "https://hexagonmi-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "海克思康"]}, {"id": "foneshare-cloud-private-hexagonmi-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://hexagonmi-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "海克思康"]}, {"id": "foneshare-cloud-private-hexagonmi-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://hexagonmi-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "海克思康"]}]}, {"id": "foneshare-cloud-private-yangnongchem", "name": "扬农化工", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-crm-yangnong-home", "name": "官网", "description": "纷享扬农化工官网登录", "iconClass": "fas fa-house-chimney", "url": "https://crm.yangnong.cn/", "tags": ["官网", "登录", "扬农化工"]}, {"id": "foneshare-cloud-private-yangnongchem-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://yangnongchem-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "扬农化工"]}, {"id": "foneshare-cloud-private-yangnongchem-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://yangnongchem-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "扬农化工"]}, {"id": "foneshare-cloud-private-yangnongchem-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-server", "url": "https://yangnongchem-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "扬农化工"]}, {"id": "foneshare-cloud-private-yangnongchem-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "扬农化工"]}, {"id": "foneshare-cloud-private-yangnongchem-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://yangnongchem-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "扬农化工"]}, {"id": "foneshare-cloud-private-yangnongchem-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-cubes", "url": "https://yangnongchem-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "扬农化工"]}, {"id": "foneshare-cloud-private-yangnongchem-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://yangnongchem-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "扬农化工"]}, {"id": "foneshare-cloud-private-yangnongchem-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://yangnongchem-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "扬农化工"]}]}, {"id": "foneshare-cloud-private-teleagi", "name": "电信智控", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-crm-teleagi-home", "name": "官网", "description": "纷享电信智控官网登录", "iconClass": "fas fa-house-chimney", "url": "https://crm.teleagi.cn/", "tags": ["官网", "登录", "电信智控"]}, {"id": "foneshare-cloud-private-teleagi-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://teleagi-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "电信智控"]}, {"id": "foneshare-cloud-private-teleagi-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://teleagi-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "电信智控"]}, {"id": "foneshare-cloud-private-teleagi-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-server", "url": "https://teleagi-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "电信智控"]}, {"id": "foneshare-cloud-private-teleagi-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "电信智控"]}, {"id": "foneshare-cloud-private-teleagi-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://teleagi-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "电信智控"]}, {"id": "foneshare-cloud-private-teleagi-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-cubes", "url": "https://teleagi-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "电信智控"]}, {"id": "foneshare-cloud-private-teleagi-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://teleagi-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "电信智控"]}, {"id": "foneshare-cloud-private-teleagi-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://teleagi-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "电信智控"]}]}, {"id": "foneshare-cloud-private-cpgc", "name": "中船(宁夏)", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-globalservice-cpgc-net-home", "name": "官网", "description": "纷享中船宁夏官网登录", "iconClass": "fas fa-house-chimney", "url": "https://globalservice.cpgc.net.cn/", "tags": ["官网", "登录", "中船宁夏"]}, {"id": "foneshare-cloud-private-cpgc-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://cpgc-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "中船(宁夏)"]}, {"id": "foneshare-cloud-private-cpgc-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://cpgc-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "中船(宁夏)"]}, {"id": "foneshare-cloud-private-cpgc-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-server", "url": "https://cpgc-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "中船(宁夏)"]}, {"id": "foneshare-cloud-private-cpgc-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "中船(宁夏)"]}, {"id": "foneshare-cloud-private-cpgc-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://cpgc-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "中船(宁夏)"]}, {"id": "foneshare-cloud-private-cpgc-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-cubes", "url": "https://cpgc-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "中船(宁夏)"]}, {"id": "foneshare-cloud-private-cpgc-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://cpgc-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "中船(宁夏)"]}, {"id": "foneshare-cloud-private-cpgc-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://cpgc-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "中船(宁夏)"]}]}, {"id": "foneshare-cloud-private-wingd", "name": "中船(法兰克福)", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-globalservice-wingd-home", "name": "官网", "description": "纷享中船法兰克福官网登录", "iconClass": "fas fa-house-chimney", "url": "https://globalservice.wingd.com/", "tags": ["官网", "登录", "中船法兰克福"]}, {"id": "foneshare-cloud-private-wingd-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://wingd-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "中船(法兰克福)"]}, {"id": "foneshare-cloud-private-wingd-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://wingd-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "中船(法兰克福)"]}, {"id": "foneshare-cloud-private-wingd-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-server", "url": "https://wingd-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "中船(法兰克福)"]}, {"id": "foneshare-cloud-private-wingd-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "中船(法兰克福)"]}, {"id": "foneshare-cloud-private-wingd-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://wingd-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "中船(法兰克福)"]}, {"id": "foneshare-cloud-private-wingd-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-cubes", "url": "https://wingd-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "中船(法兰克福)"]}, {"id": "foneshare-cloud-private-wingd-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://wingd-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "中船(法兰克福)"]}, {"id": "foneshare-cloud-private-wingd-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://wingd-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "中船(法兰克福)"]}]}, {"id": "foneshare-cloud-private-kehua", "name": "科华云", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-crm-kehua-home", "name": "官网", "description": "纷享科华云官网登录", "iconClass": "fas fa-house-chimney", "url": "https://crm.kehua.com/", "tags": ["官网", "登录", "科华云"]}, {"id": "foneshare-cloud-private-kehua-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://kehua-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "科华云"]}, {"id": "foneshare-cloud-private-kehua-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://kehua-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "科华云"]}, {"id": "foneshare-cloud-private-kehua-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-server", "url": "https://kehua-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "科华云"]}, {"id": "foneshare-cloud-private-kehua-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "科华云"]}, {"id": "foneshare-cloud-private-kehua-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://kehua-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "科华云"]}, {"id": "foneshare-cloud-private-kehua-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://kehua-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "科华云"]}, {"id": "foneshare-cloud-private-kehua-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://kehua-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "科华云"]}, {"id": "foneshare-cloud-private-kehua-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://kehua-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "科华云"]}]}, {"id": "foneshare-cloud-private-jingbo", "name": "京博云", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-ncrm-chambroad-home", "name": "官网", "description": "纷享京博云官网登录", "iconClass": "fas fa-house-chimney", "url": "https://ncrm.chambroad.com/", "tags": ["官网", "登录", "京博云"]}, {"id": "foneshare-cloud-private-jingbo-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://jingbo-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "京博云"]}, {"id": "foneshare-cloud-private-jingbo-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://jingbo-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "京博云"]}, {"id": "foneshare-cloud-private-jingbo-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-server", "url": "https://jingbo-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "京博云"]}, {"id": "foneshare-cloud-private-jingbo-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "京博云"]}, {"id": "foneshare-cloud-private-jingbo-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://jingbo-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "京博云"]}, {"id": "foneshare-cloud-private-jingbo-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://jingbo-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "京博云"]}, {"id": "foneshare-cloud-private-jingbo-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://jingbo-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "京博云"]}, {"id": "foneshare-cloud-private-jingbo-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://jingbo-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "京博云"]}]}, {"id": "foneshare-cloud-private-tbea", "name": "特变电工", "icon": "⛅️", "iconClass": "fas fa-building", "sites": [{"id": "foneshare-cloud-private-icrm-tbea-home", "name": "官网", "description": "纷享特变电工官网登录", "iconClass": "fas fa-house-chimney", "url": "https://icrm.tbea.com/", "tags": ["官网", "登录", "特变电工"]}, {"id": "foneshare-cloud-private-tbea-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://tbea-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "特变电工"]}, {"id": "foneshare-cloud-private-tbea-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://tbea-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "console", "特变电工"]}, {"id": "foneshare-cloud-private-tbea-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-server", "url": "https://tbea-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "admin", "特变电工"]}, {"id": "foneshare-cloud-private-tbea-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "特变电工"]}, {"id": "foneshare-cloud-private-tbea-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://tbea-prod.foneshare.cn/job/", "tags": ["xxl-job", "task", "特变电工"]}, {"id": "foneshare-cloud-private-tbea-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://tbea-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "特变电工"]}, {"id": "foneshare-cloud-private-tbea-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://tbea-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "特变电工"]}, {"id": "foneshare-cloud-private-tbea-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://tbea-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "特变电工"]}]}]}]}]}